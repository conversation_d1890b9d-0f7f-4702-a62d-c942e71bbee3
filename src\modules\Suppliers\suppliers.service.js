import { prisma } from '../../config/prismaClient.js';
import { hashPassword } from '../../utils/hashing/hash.js';
import crypto from 'crypto';

/**
 * Suppliers Service
 * Database operations and business logic for supplier management
 * Uses exact field names from the Users and Suppliers Prisma models
 */

/**
 * Get suppliers with pagination, search, and filtering
 */
export const getSuppliersService = async (filters) => {
  const {
    page = 1,
    limit = 20,
    search = '',
    verificationStatus,
    status,
    sort = 'updatedAt',
    order = 'desc'
  } = filters;

  const skip = (page - 1) * limit;
  
  // Build where clause
  const whereClause = {
    Suppliers: {
      isNot: null // Only get users who are suppliers
    }
  };

  // Add search filter (searches Name and Email)
  if (search) {
    whereClause.OR = [
      {
        Name: {
          contains: search,
          mode: 'insensitive'
        }
      },
      {
        Email: {
          contains: search,
          mode: 'insensitive'
        }
      }
    ];
  }

  // Add verification status filter
  if (verificationStatus) {
    if (verificationStatus === 'verified') {
      whereClause.EmailConfirmed = true;
    } else if (verificationStatus === 'pending') {
      whereClause.EmailConfirmed = false;
    }
  }

  // Add status filter
  if (status) {
    if (status === 'active') {
      whereClause.LockoutEnabled = false;
    } else if (status === 'banned') {
      whereClause.LockoutEnabled = true;
    }
  }

  // Build order by clause
  const orderBy = {};
  if (sort === 'createdAt' || sort === 'updatedAt') {
    // These fields don't exist in Users table, so we'll use a proxy
    orderBy.Id = order; // Use Id as proxy for creation order
  } else {
    orderBy[sort] = order;
  }

  // Execute query
  const [suppliers, total] = await Promise.all([
    prisma.users.findMany({
      where: whereClause,
      include: {
        Suppliers: {
          include: {
            ActivityCategories: true
          }
        }
      },
      orderBy,
      skip,
      take: limit
    }),
    prisma.users.count({
      where: whereClause
    })
  ]);

  return {
    suppliers,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
};

/**
 * Get single supplier by ID
 */
export const getSupplierByIdService = async (supplierId) => {
  const supplier = await prisma.users.findUnique({
    where: {
      Id: supplierId
    },
    include: {
      Suppliers: {
        include: {
          ActivityCategories: true
        }
      },
      UserRoles: {
        include: {
          AspNetRoles: true
        }
      }
    }
  });

  // Check if user is actually a supplier
  if (supplier && !supplier.Suppliers) {
    return null;
  }

  return supplier;
};

/**
 * Get supplier products with pagination
 */
export const getSupplierProductsService = async (supplierId, filters) => {
  const {
    page = 1,
    limit = 10
  } = filters;

  const skip = (page - 1) * limit;

  // First verify supplier exists
  const supplier = await getSupplierByIdService(supplierId);
  if (!supplier) {
    throw new Error('Supplier not found');
  }

  // Build where clause for products
  const whereClause = {
    SupplierId: supplierId,
    Deleted: false
  };

  // Execute query
  const [products, total] = await Promise.all([
    prisma.products.findMany({
      where: whereClause,
      include: {
        Categories: true,
        Images: {
          where: { Deleted: false }
        },
        ProductAttribute: {
          where: { Deleted: false }
        },
        ProductVariant: {
          where: { Deleted: false }
        }
      },
      orderBy: {
        CreatedDate: 'desc'
      },
      skip,
      take: limit
    }),
    prisma.products.count({
      where: whereClause
    })
  ]);

  return {
    products,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
};

/**
 * Create new supplier
 */
export const createSupplierService = async (supplierData) => {
  const {
    name,
    email,
    password,
    phone,
    address,
    contactPerson,
    categories
  } = supplierData;

  // Check if email already exists
  const existingUser = await prisma.users.findFirst({
    where: {
      Email: email
    }
  });

  if (existingUser) {
    throw new Error('Email already exists');
  }

  // Find activity category if provided
  let activityCategoryId = null;
  if (categories) {
    const activityCategory = await prisma.activityCategories.findFirst({
      where: {
        Name: categories,
        Deleted: false
      }
    });
    if (activityCategory) {
      activityCategoryId = activityCategory.ID;
    }
  }

  // Hash password
  const hashedPassword = hashPassword(password);
  
  // Generate unique ID
  const userId = crypto.randomUUID();
  
  // Create user and supplier in transaction
  const result = await prisma.$transaction(async (tx) => {
    // Create user
    const user = await tx.users.create({
      data: {
        Id: userId,
        Name: contactPerson, // Use contactPerson as Name
        Email: email,
        UserName: email, // Use email as username
        NormalizedUserName: email.toUpperCase(),
        NormalizedEmail: email.toUpperCase(),
        PasswordHash: hashedPassword,
        SecurityStamp: crypto.randomUUID(),
        ConcurrencyStamp: crypto.randomUUID(),
        PhoneNumber: phone || null,
        Address: address || null,
        BusinessType: name || null, // Store business name in BusinessType
        EmailConfirmed: false, // Start as pending verification
        PhoneNumberConfirmed: false,
        TwoFactorEnabled: false,
        LockoutEnabled: false,
        AccessFailedCount: 0
      }
    });

    // Create supplier profile
    const supplier = await tx.suppliers.create({
      data: {
        Id: userId,
        ActivityCategoryId: activityCategoryId
      }
    });

    return { user, supplier };
  });

  // Return the created supplier with category info
  return await getSupplierByIdService(userId);
};

/**
 * Update supplier verification status
 */
export const updateSupplierVerificationStatusService = async (supplierId, verificationStatus) => {
  // Check if supplier exists
  const existingSupplier = await getSupplierByIdService(supplierId);
  if (!existingSupplier) {
    throw new Error('Supplier not found');
  }

  // Update verification status
  const updatedUser = await prisma.users.update({
    where: { Id: supplierId },
    data: {
      EmailConfirmed: verificationStatus === 'verified',
      SecurityStamp: crypto.randomUUID() // Invalidate existing tokens
    }
  });

  return await getSupplierByIdService(supplierId);
};

/**
 * Ban supplier
 */
export const banSupplierService = async (supplierId) => {
  // Check if supplier exists
  const existingSupplier = await getSupplierByIdService(supplierId);
  if (!existingSupplier) {
    throw new Error('Supplier not found');
  }

  // Ban supplier
  await prisma.users.update({
    where: { Id: supplierId },
    data: {
      LockoutEnabled: true,
      LockoutEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      SecurityStamp: crypto.randomUUID() // Invalidate existing tokens
    }
  });

  return await getSupplierByIdService(supplierId);
};

/**
 * Unban supplier
 */
export const unbanSupplierService = async (supplierId) => {
  // Check if supplier exists
  const existingSupplier = await getSupplierByIdService(supplierId);
  if (!existingSupplier) {
    throw new Error('Supplier not found');
  }

  // Unban supplier
  await prisma.users.update({
    where: { Id: supplierId },
    data: {
      LockoutEnabled: false,
      LockoutEnd: null,
      SecurityStamp: crypto.randomUUID() // Invalidate existing tokens
    }
  });

  return await getSupplierByIdService(supplierId);
};

/**
 * Delete supplier
 */
export const deleteSupplierService = async (supplierId) => {
  // Check if supplier exists
  const existingSupplier = await getSupplierByIdService(supplierId);
  if (!existingSupplier) {
    throw new Error('Supplier not found');
  }

  // Delete supplier (this will cascade delete the user due to FK constraint)
  await prisma.suppliers.delete({
    where: { Id: supplierId }
  });

  return true;
};
